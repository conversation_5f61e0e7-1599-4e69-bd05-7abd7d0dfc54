# AgentZero Chat Interface Usage Guide

## Objective
Provide comprehensive instructions for accessing and using the AgentZero VSCode extension chat interface that has been successfully compiled and is running in development mode.

## Implementation Plan

1. **Access Chat Interface via Activity Bar**
   - Dependencies: None
   - Notes: Primary method for accessing the chat interface - most intuitive for users
   - Files: package.json:47-51, src/extension.ts:20-24
   - Status: Not Started

2. **Access Chat Interface via Command Palette**
   - Dependencies: None
   - Notes: Alternative method using VSCode command system - useful if Activity Bar icon is not visible
   - Files: package.json:18-21, src/extension.ts:58-61
   - Status: Not Started

3. **Use Code Analysis Feature**
   - Dependencies: Task 1 or 2
   - Notes: Right-click context menu integration for analyzing selected code
   - Files: package.json:64-69, src/extension.ts:65-95
   - Status: Not Started

4. **Verify Chat Functionality**
   - Dependencies: Task 1, 2
   - Notes: Test basic chat operations and model selection
   - Files: src/chat-webview.ts:59-157
   - Status: Not Started

5. **Access Agent Dashboard**
   - Dependencies: None
   - Notes: Secondary interface for monitoring agent status and system health
   - Files: package.json:52-56, src/extension.ts:26-30
   - Status: Not Started

## Verification Criteria
- Chat interface opens successfully in VSCode sidebar
- Model selection dropdown displays available Ollama models (qwen3:0.6b confirmed)
- Chat messages can be sent and responses received from AI
- Code analysis context menu appears when text is selected
- Agent dashboard displays system status information

## Potential Risks and Mitigations

1. **Activity Bar Icon Not Visible**
   Mitigation: Use Command Palette method or restart VSCode development instance

2. **Ollama Service Connection Issues**
   Mitigation: Verify Ollama is running on localhost:11434 and qwen3:0.6b model is available

3. **Chat Interface Not Responding**
   Mitigation: Check VSCode Developer Console for errors and restart extension if needed

4. **Code Analysis Feature Not Working**
   Mitigation: Ensure text is properly selected and right-click context menu is accessible

## Alternative Approaches

1. **Direct Command Execution**: Use VSCode command palette to execute specific AgentZero commands
2. **Keyboard Shortcuts**: Set up custom keybindings for frequently used commands
3. **Status Bar Integration**: Monitor extension status through VSCode status bar notifications

## Detailed Access Instructions

### Method 1: Activity Bar Access
The extension registers two Activity Bar containers:
- **AgentZero Chat** (comment-discussion icon) - Primary chat interface
- **AgentZero Agents** (organization icon) - Agent management dashboard

### Method 2: Command Palette Access
Available commands through Ctrl+Shift+P (Cmd+Shift+P on Mac):
- "Open AgentZero Chat" - Opens chat interface
- "Open Agent Dashboard" - Opens agent management interface
- "Analyze Selected Code with AI" - Analyzes selected code
- "Show Agent Status" - Displays current agent status

### Method 3: Context Menu Access
Right-click on selected code to access:
- "Analyze Selected Code with AI" - Sends code to chat interface for analysis

### Chat Interface Features
- Model selection dropdown (populated from Ollama service)
- Message input with send button
- Chat history display
- Clear chat functionality
- Welcome message and status indicators

### Agent Dashboard Features
- Agent status monitoring
- System health indicators
- Task completion notifications
- Critical system alerts

## Technical Implementation Details

The chat interface is implemented as a WebviewViewProvider registered in the Activity Bar with:
- View ID: agentzero.chatView
- Container ID: agentzero-chat
- Integration with Ollama client for AI responses
- Message handling for code analysis requests

The extension automatically activates when VSCode loads and registers all necessary providers and commands for immediate use.