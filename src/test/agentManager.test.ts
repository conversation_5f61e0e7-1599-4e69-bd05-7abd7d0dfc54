import * as vscode from 'vscode';
import { AgentManager } from '../services/agentManager';

export async function testAgentManager(): Promise<void> {
  // Create a mock context for testing
  const mockContext: vscode.ExtensionContext = {
    subscriptions: [],
    workspaceState: {} as any,
    globalState: {} as any,
    extensionUri: vscode.Uri.file('/test'),
    extensionPath: '/test',
    asAbsolutePath: (relativePath: string) => `/test/${relativePath}`,
    storageUri: undefined,
    storagePath: undefined,
    globalStorageUri: vscode.Uri.file('/test/global'),
    globalStoragePath: '/test/global',
    logUri: vscode.Uri.file('/test/log'),
    logPath: '/test/log',
    extensionMode: vscode.ExtensionMode.Test,
    environmentVariableCollection: {} as any,
    secrets: {} as any,
    extension: {} as any,
    languageModelAccessInformation: {} as any
  };

  const agentManager = new AgentManager(mockContext);
  
  console.log('Testing Agent Manager...');
  
  // Test system status
  const status = agentManager.getSystemStatus();
  console.log('System Status:', status);
  
  // Test agent status
  const agent1 = agentManager.getAgentStatus('agent-1');
  const agent2 = agentManager.getAgentStatus('agent-2');
  
  console.log('Agent 1:', agent1?.name, agent1?.status);
  console.log('Agent 2:', agent2?.name, agent2?.status);
  
  // Test agent restart
  agentManager.restartAgent('agent-1');
  console.log('Agent 1 restarted');
  
  // Clean up
  agentManager.dispose();
  console.log('Agent Manager test completed');
}

// Export for use in extension