// The module 'vscode' contains the VS Code extensibility API
// Import the module and reference it with the alias vscode in your code below
import * as vscode from 'vscode';
import { ChatWebviewProvider } from './chat-webview';
import { AgentManager } from './services/agentManager';
import { AgentDashboardProvider } from './webviews/agentDashboard';

// This method is called when your extension is activated
// Your extension is activated the very first time the command is executed
export function activate(context: vscode.ExtensionContext) {

  // Use the console to output diagnostic information (console.log) and errors (console.error)
  // This line of code will only be executed once when your extension is activated
  console.log('Congratulations, your extension "agentzero" is now active!');

  // Initialize Agent Management System
  const agentManager = new AgentManager(context);
  context.subscriptions.push(agentManager);

  // Register the chat webview provider
  const chatProvider = new ChatWebviewProvider(context.extensionUri);
  context.subscriptions.push(
    vscode.window.registerWebviewViewProvider(ChatWebviewProvider.viewType, chatProvider)
  );

  // Register the agent dashboard provider
  const agentDashboardProvider = new AgentDashboardProvider(context.extensionUri, agentManager);
  context.subscriptions.push(
    vscode.window.registerWebviewViewProvider(AgentDashboardProvider.viewType, agentDashboardProvider)
  );

  // The command has been defined in the package.json file
  // Now provide the implementation of the command with registerCommand
  // The commandId parameter must match the command field in package.json
  const disposable = vscode.commands.registerCommand('agentzero.helloWorld', () => {
    // The code you place here will be executed every time your command is executed
    // Display a message box to the user
    vscode.window.showInformationMessage('Hello World from AgentZero!');
  });

  context.subscriptions.push(disposable);

  const reverse = vscode.commands.registerCommand('agentzero.reverseText', () => {
    const editor = vscode.window.activeTextEditor;
    if (editor) {
      const selection = editor.selection;
      const selectedText = editor.document.getText(selection);
      const reversedText = selectedText.split('').reverse().join('');
      editor.edit(editBuilder => {
        editBuilder.replace(selection, reversedText);
      });
    }
  });

  context.subscriptions.push(reverse);

  // Command to open chat interface
  const openChat = vscode.commands.registerCommand('agentzero.openChat', () => {
    vscode.commands.executeCommand('workbench.view.extension.agentzero-chat');
  });

  context.subscriptions.push(openChat);

  // Command to analyze selected code
  const analyzeCode = vscode.commands.registerCommand('agentzero.analyzeCode', async () => {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      vscode.window.showErrorMessage('No active editor found');
      return;
    }

    const selection = editor.selection;
    const selectedText = editor.document.getText(selection);
    
    if (!selectedText.trim()) {
      vscode.window.showErrorMessage('Please select some code to analyze');
      return;
    }

    const language = editor.document.languageId;
    
    // Send code to chat interface for analysis
    if (chatProvider._view) {
      chatProvider._view.webview.postMessage({
        type: 'analyzeCode',
        code: selectedText,
        language: language
      });
      
      // Show the chat view
      vscode.commands.executeCommand('workbench.view.extension.agentzero-chat');
    } else {
      vscode.window.showErrorMessage('Chat interface is not available');
    }
  });

  context.subscriptions.push(analyzeCode);

  // Agent Management Commands
  const showAgentStatus = vscode.commands.registerCommand('agentzero.showAgentStatus', () => {
    agentManager.showStatus();
  });

  context.subscriptions.push(showAgentStatus);

  const startMonitoring = vscode.commands.registerCommand('agentzero.startMonitoring', () => {
    agentManager.startMonitoring();
    vscode.window.showInformationMessage('Agent monitoring started');
  });

  context.subscriptions.push(startMonitoring);

  const stopMonitoring = vscode.commands.registerCommand('agentzero.stopMonitoring', () => {
    agentManager.stopMonitoring();
    vscode.window.showInformationMessage('Agent monitoring stopped');
  });

  context.subscriptions.push(stopMonitoring);

  const openAgentDashboard = vscode.commands.registerCommand('agentzero.openAgentDashboard', () => {
    vscode.commands.executeCommand('workbench.view.extension.agentzero-agents');
  });

  context.subscriptions.push(openAgentDashboard);

  // Agent event listeners for user notifications
  agentManager.on('agentCompleted', (data) => {
    vscode.window.showInformationMessage(`Agent ${data.agent.name} completed task`);
  });

  agentManager.on('allAgentsCompleted', (data) => {
    vscode.window.showInformationMessage('All agents have completed their tasks - System ready for execution');
  });

  agentManager.on('healthCheck', (data) => {
    if (data.systemHealth === 'critical') {
      vscode.window.showErrorMessage('Critical system health detected - Check agent dashboard for details');
    }
  });
}

// This method is called when your extension is deactivated
export function deactivate() { }