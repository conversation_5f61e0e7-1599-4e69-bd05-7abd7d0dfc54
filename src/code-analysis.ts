import * as vscode from 'vscode';
import { OllamaClient, OllamaMessage } from './ollama-client';

export interface CodeAnalysisResult {
    suggestions: CodeSuggestion[];
    summary: string;
    confidence: number;
}

export interface CodeSuggestion {
    type: 'improvement' | 'bug' | 'optimization' | 'style' | 'security';
    description: string;
    lineRange?: { start: number; end: number };
    originalCode?: string;
    suggestedCode?: string;
    reasoning: string;
    priority: 'low' | 'medium' | 'high';
}

export class CodeAnalysisService {
    private ollamaClient: OllamaClient;

    constructor() {
        this.ollamaClient = new OllamaClient();
    }

    /**
     * Analyze code and provide improvement suggestions
     */
    async analyzeCode(
        code: string,
        language: string,
        model: string,
        context?: string
    ): Promise<CodeAnalysisResult> {
        const systemPrompt = this.createSystemPrompt(language);
        const userPrompt = this.createUserPrompt(code, context);

        const messages: OllamaMessage[] = [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
        ];

        try {
            const response = await this.ollamaClient.chat(model, messages, {
                temperature: 0.3, // Lower temperature for more consistent analysis
                top_p: 0.9
            });

            return this.parseAnalysisResponse(response);
        } catch (error) {
            throw new Error(`Code analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * Get improvement suggestions for selected code
     */
    async getImprovementSuggestions(
        code: string,
        language: string,
        model: string,
        focusArea?: 'performance' | 'readability' | 'maintainability' | 'security'
    ): Promise<CodeSuggestion[]> {
        const systemPrompt = this.createImprovementSystemPrompt(language, focusArea);
        const userPrompt = `Please analyze this ${language} code and provide specific improvement suggestions:\n\n\`\`\`${language}\n${code}\n\`\`\``;

        const messages: OllamaMessage[] = [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
        ];

        try {
            const response = await this.ollamaClient.chat(model, messages, {
                temperature: 0.3,
                top_p: 0.9
            });

            const result = this.parseAnalysisResponse(response);
            return result.suggestions;
        } catch (error) {
            throw new Error(`Improvement analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * Explain code functionality
     */
    async explainCode(
        code: string,
        language: string,
        model: string
    ): Promise<string> {
        const systemPrompt = `You are a helpful code explainer. Explain code clearly and concisely, focusing on:
1. What the code does
2. How it works
3. Key concepts or patterns used
4. Any potential issues or considerations

Be educational and accessible to developers of different skill levels.`;

        const userPrompt = `Please explain this ${language} code:\n\n\`\`\`${language}\n${code}\n\`\`\``;

        const messages: OllamaMessage[] = [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
        ];

        try {
            return await this.ollamaClient.chat(model, messages, {
                temperature: 0.4,
                top_p: 0.9
            });
        } catch (error) {
            throw new Error(`Code explanation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    private createSystemPrompt(language: string): string {
        return `You are an expert ${language} code analyzer. Your task is to analyze code and provide constructive feedback in a structured format.

IMPORTANT: You must respond with a valid JSON object that follows this exact structure:
{
  "summary": "Brief overall assessment of the code",
  "confidence": 0.85,
  "suggestions": [
    {
      "type": "improvement|bug|optimization|style|security",
      "description": "Clear description of the issue or suggestion",
      "reasoning": "Explanation of why this matters",
      "priority": "low|medium|high",
      "originalCode": "problematic code snippet (if applicable)",
      "suggestedCode": "improved code snippet (if applicable)"
    }
  ]
}

Guidelines:
- Focus on actionable improvements
- Prioritize suggestions by impact
- Include code snippets when suggesting changes
- Be specific and constructive
- Consider best practices for ${language}
- Look for: bugs, performance issues, readability problems, security vulnerabilities, code smells
- Confidence should be between 0.0 and 1.0`;
    }

    private createImprovementSystemPrompt(language: string, focusArea?: string): string {
        const focus = focusArea ? `Focus specifically on ${focusArea} improvements.` : '';
        
        return `You are an expert ${language} code improvement specialist. ${focus}

IMPORTANT: You must respond with a valid JSON object that follows this exact structure:
{
  "summary": "Brief summary of improvement opportunities",
  "confidence": 0.85,
  "suggestions": [
    {
      "type": "improvement|optimization|style|security",
      "description": "Clear description of the improvement",
      "reasoning": "Why this improvement is beneficial",
      "priority": "low|medium|high",
      "originalCode": "current code snippet",
      "suggestedCode": "improved code snippet"
    }
  ]
}

Focus on practical, implementable improvements that enhance code quality.`;
    }

    private createUserPrompt(code: string, context?: string): string {
        let prompt = `Please analyze this code for potential improvements, bugs, and optimizations:\n\n\`\`\`\n${code}\n\`\`\``;
        
        if (context) {
            prompt += `\n\nAdditional context: ${context}`;
        }
        
        return prompt;
    }

    private parseAnalysisResponse(response: string): CodeAnalysisResult {
        try {
            // Try to extract JSON from the response
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('No JSON found in response');
            }

            const parsed = JSON.parse(jsonMatch[0]);
            
            // Validate the structure
            if (!parsed.summary || !Array.isArray(parsed.suggestions)) {
                throw new Error('Invalid response structure');
            }

            // Ensure confidence is a number between 0 and 1
            const confidence = typeof parsed.confidence === 'number' ? 
                Math.max(0, Math.min(1, parsed.confidence)) : 0.5;

            // Validate and clean suggestions
            const suggestions: CodeSuggestion[] = parsed.suggestions
                .filter((s: any) => s.type && s.description && s.reasoning && s.priority)
                .map((s: any) => ({
                    type: s.type,
                    description: s.description,
                    reasoning: s.reasoning,
                    priority: s.priority,
                    originalCode: s.originalCode || undefined,
                    suggestedCode: s.suggestedCode || undefined,
                    lineRange: s.lineRange || undefined
                }));

            return {
                summary: parsed.summary,
                confidence,
                suggestions
            };
        } catch (error) {
            // Fallback: create a basic analysis result from the raw response
            return {
                summary: 'Analysis completed, but response format was unexpected.',
                confidence: 0.3,
                suggestions: [{
                    type: 'improvement',
                    description: 'AI provided analysis in text format',
                    reasoning: response.substring(0, 500) + (response.length > 500 ? '...' : ''),
                    priority: 'medium'
                }]
            };
        }
    }
}