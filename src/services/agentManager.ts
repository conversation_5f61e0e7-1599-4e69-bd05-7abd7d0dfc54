import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { EventEmitter } from 'events';

export interface AgentStatus {
  agentId: string;
  name: string;
  status: 'idle' | 'running' | 'completed' | 'failed' | 'timeout';
  progress: number;
  lastUpdate: Date;
  workProducts: string[];
  errors?: string[];
  healthScore: number;
  performance: {
    tasksCompleted: number;
    averageResponseTime: number;
    errorRate: number;
  };
}

export interface SystemStatus {
  agents: AgentStatus[];
  systemHealth: 'healthy' | 'degraded' | 'critical';
  executionReady: boolean;
  lastExecution?: Date;
  totalTasks: number;
  activeTasks: number;
}

export class AgentManager extends EventEmitter {
  private agents: Map<string, AgentStatus> = new Map();
  private monitoringInterval: NodeJS.Timeout | null = null;
  private fileWatcher: vscode.FileSystemWatcher | null = null;
  private outputChannel: vscode.OutputChannel;
  private statusBarItem: vscode.StatusBarItem;
  private isMonitoring: boolean = false;

  constructor(private context: vscode.ExtensionContext) {
    super();
    this.outputChannel = vscode.window.createOutputChannel('AgentZero Manager');
    this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
    this.statusBarItem.command = 'agentzero.showAgentStatus';
    this.statusBarItem.show();
    
    this.initializeAgents();
    this.setupFileWatcher();
    this.startMonitoring();
  }

  private initializeAgents(): void {
    // Initialize the two primary agents
    const agent1: AgentStatus = {
      agentId: 'agent-1',
      name: 'Code Analysis Agent',
      status: 'idle',
      progress: 0,
      lastUpdate: new Date(),
      workProducts: [],
      healthScore: 100,
      performance: {
        tasksCompleted: 0,
        averageResponseTime: 0,
        errorRate: 0
      }
    };

    const agent2: AgentStatus = {
      agentId: 'agent-2',
      name: 'Error Monitoring Agent',
      status: 'idle',
      progress: 0,
      lastUpdate: new Date(),
      workProducts: [],
      healthScore: 100,
      performance: {
        tasksCompleted: 0,
        averageResponseTime: 0,
        errorRate: 0
      }
    };

    this.agents.set('agent-1', agent1);
    this.agents.set('agent-2', agent2);
    
    this.log('Agent Manager initialized with 2 agents');
    this.updateStatusBar();
  }

  private setupFileWatcher(): void {
    // Watch for file changes to detect agent activity
    const pattern = new vscode.RelativePattern(
      vscode.workspace.workspaceFolders![0],
      '**/*.{ts,js,json,md}'
    );
    
    this.fileWatcher = vscode.workspace.createFileSystemWatcher(pattern);
    
    this.fileWatcher.onDidChange((uri) => {
      this.handleFileChange(uri, 'modified');
    });
    
    this.fileWatcher.onDidCreate((uri) => {
      this.handleFileChange(uri, 'created');
    });
    
    this.fileWatcher.onDidDelete((uri) => {
      this.handleFileChange(uri, 'deleted');
    });

    this.log('File system watcher initialized');
  }

  private handleFileChange(uri: vscode.Uri, changeType: string): void {
    const fileName = path.basename(uri.fsPath);
    const relativePath = vscode.workspace.asRelativePath(uri);
    
    this.log(`File ${changeType}: ${relativePath}`);
    
    // Detect which agent might be responsible for the change
    let responsibleAgent: string | null = null;
    
    if (uri.fsPath.includes('/src/') && fileName.endsWith('.ts')) {
      responsibleAgent = 'agent-1'; // Code Analysis Agent
    } else if (fileName.includes('error') || fileName.includes('monitor')) {
      responsibleAgent = 'agent-2'; // Error Monitoring Agent
    }
    
    if (responsibleAgent) {
      this.updateAgentActivity(responsibleAgent, relativePath);
    }
    
    // Trigger health check after file changes
    setTimeout(() => {
      this.performHealthCheck();
    }, 2000); // 2-second delay to let agent finish
  }

  private updateAgentActivity(agentId: string, workProduct: string): void {
    const agent = this.agents.get(agentId);
    if (agent) {
      agent.status = 'running';
      agent.lastUpdate = new Date();
      agent.workProducts.push(workProduct);
      agent.progress = Math.min(agent.progress + 10, 100);
      
      this.log(`Agent ${agent.name} is working on: ${workProduct}`);
      this.updateStatusBar();
      this.emit('agentActivity', { agentId, workProduct });
    }
  }

  public startMonitoring(): void {
    if (this.isMonitoring) {
      return;
    }
    
    this.isMonitoring = true;
    this.monitoringInterval = setInterval(() => {
      this.performHealthCheck();
      this.updateAgentStatuses();
      this.checkForCompletedTasks();
    }, 5000); // Check every 5 seconds
    
    this.log('Agent monitoring started');
    this.updateStatusBar();
  }

  public stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }
    
    this.isMonitoring = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    
    this.log('Agent monitoring stopped');
    this.updateStatusBar();
  }

  private async performHealthCheck(): Promise<void> {
    try {
      // Check TypeScript compilation
      const compileResult = await this.runCommand('npx tsc --noEmit');
      
      // Check ESLint
      const lintResult = await this.runCommand('npx eslint src/ --quiet');
      
      // Update system health based on checks
      const systemHealth = this.calculateSystemHealth(compileResult, lintResult);
      
      this.emit('healthCheck', {
        timestamp: new Date(),
        compilation: compileResult.success,
        linting: lintResult.success,
        systemHealth
      });
      
    } catch (error) {
      this.log(`Health check failed: ${error}`);
    }
  }

  private calculateSystemHealth(compileResult: any, lintResult: any): 'healthy' | 'degraded' | 'critical' {
    if (!compileResult.success) {
      return 'critical';
    }
    if (!lintResult.success) {
      return 'degraded';
    }
    return 'healthy';
  }

  private async runCommand(command: string): Promise<{ success: boolean; output: string }> {
    return new Promise((resolve) => {
      const { exec } = require('child_process');
      exec(command, { cwd: vscode.workspace.workspaceFolders![0].uri.fsPath }, (error: any, stdout: string, stderr: string) => {
        resolve({
          success: !error,
          output: error ? stderr : stdout
        });
      });
    });
  }

  private updateAgentStatuses(): void {
    const now = new Date();
    
    this.agents.forEach((agent, agentId) => {
      const timeSinceUpdate = now.getTime() - agent.lastUpdate.getTime();
      
      // Auto-complete agents that haven't been active for 30 seconds
      if (agent.status === 'running' && timeSinceUpdate > 30000) {
        agent.status = 'completed';
        agent.progress = 100;
        this.log(`Agent ${agent.name} marked as completed`);
        this.emit('agentCompleted', { agentId, agent });
      }
      
      // Update health score based on performance
      this.updateAgentHealth(agent);
    });
    
    this.updateStatusBar();
  }

  private updateAgentHealth(agent: AgentStatus): void {
    // Simple health scoring algorithm
    let healthScore = 100;
    
    // Reduce score for errors
    if (agent.errors && agent.errors.length > 0) {
      healthScore -= agent.errors.length * 10;
    }
    
    // Reduce score for poor performance
    if (agent.performance.errorRate > 0.1) {
      healthScore -= 20;
    }
    
    // Reduce score for long response times
    if (agent.performance.averageResponseTime > 10000) {
      healthScore -= 15;
    }
    
    agent.healthScore = Math.max(0, healthScore);
  }

  private checkForCompletedTasks(): void {
    const completedAgents = Array.from(this.agents.values()).filter(
      agent => agent.status === 'completed'
    );
    
    if (completedAgents.length === 2) {
      this.log('Both agents have completed their tasks');
      this.emit('allAgentsCompleted', { agents: completedAgents });
      
      // Reset agents for next cycle
      setTimeout(() => {
        this.resetAgents();
      }, 5000);
    }
  }

  private resetAgents(): void {
    this.agents.forEach((agent) => {
      agent.status = 'idle';
      agent.progress = 0;
      agent.workProducts = [];
      agent.lastUpdate = new Date();
    });
    
    this.log('Agents reset for next cycle');
    this.updateStatusBar();
  }

  public getSystemStatus(): SystemStatus {
    const agents = Array.from(this.agents.values());
    const activeAgents = agents.filter(a => a.status === 'running');
    const healthyAgents = agents.filter(a => a.healthScore > 70);
    
    let systemHealth: 'healthy' | 'degraded' | 'critical' = 'healthy';
    if (healthyAgents.length < agents.length) {
      systemHealth = healthyAgents.length === 0 ? 'critical' : 'degraded';
    }
    
    return {
      agents,
      systemHealth,
      executionReady: agents.every(a => a.status === 'completed' || a.status === 'idle'),
      totalTasks: agents.reduce((sum, a) => sum + a.performance.tasksCompleted, 0),
      activeTasks: activeAgents.length
    };
  }

  public getAgentStatus(agentId: string): AgentStatus | undefined {
    return this.agents.get(agentId);
  }

  public restartAgent(agentId: string): void {
    const agent = this.agents.get(agentId);
    if (agent) {
      agent.status = 'idle';
      agent.progress = 0;
      agent.errors = [];
      agent.lastUpdate = new Date();
      
      this.log(`Agent ${agent.name} restarted`);
      this.updateStatusBar();
      this.emit('agentRestarted', { agentId, agent });
    }
  }

  public pauseAgent(agentId: string): void {
    const agent = this.agents.get(agentId);
    if (agent && agent.status === 'running') {
      agent.status = 'idle';
      this.log(`Agent ${agent.name} paused`);
      this.updateStatusBar();
    }
  }

  public resumeAgent(agentId: string): void {
    const agent = this.agents.get(agentId);
    if (agent && agent.status === 'idle') {
      agent.status = 'running';
      agent.lastUpdate = new Date();
      this.log(`Agent ${agent.name} resumed`);
      this.updateStatusBar();
    }
  }

  private updateStatusBar(): void {
    const status = this.getSystemStatus();
    const activeCount = status.activeTasks;
    const healthIcon = status.systemHealth === 'healthy' ? '🟢' : 
                      status.systemHealth === 'degraded' ? '🟡' : '🔴';
    
    this.statusBarItem.text = `${healthIcon} AgentZero: ${activeCount} active`;
    this.statusBarItem.tooltip = `System Health: ${status.systemHealth}\nActive Tasks: ${activeCount}\nTotal Completed: ${status.totalTasks}`;
  }

  private log(message: string): void {
    const timestamp = new Date().toLocaleTimeString();
    this.outputChannel.appendLine(`[${timestamp}] ${message}`);
  }

  public showStatus(): void {
    const status = this.getSystemStatus();
    const statusMessage = this.formatStatusMessage(status);
    
    vscode.window.showInformationMessage('Agent Status', 'Show Details').then(selection => {
      if (selection === 'Show Details') {
        this.outputChannel.show();
        this.outputChannel.appendLine('\n' + '='.repeat(50));
        this.outputChannel.appendLine('AGENT STATUS REPORT');
        this.outputChannel.appendLine('='.repeat(50));
        this.outputChannel.appendLine(statusMessage);
      }
    });
  }

  private formatStatusMessage(status: SystemStatus): string {
    let message = `System Health: ${status.systemHealth}\n`;
    message += `Execution Ready: ${status.executionReady}\n`;
    message += `Active Tasks: ${status.activeTasks}\n`;
    message += `Total Completed: ${status.totalTasks}\n\n`;
    
    message += 'Agent Details:\n';
    status.agents.forEach(agent => {
      message += `\n${agent.name} (${agent.agentId}):\n`;
      message += `  Status: ${agent.status}\n`;
      message += `  Progress: ${agent.progress}%\n`;
      message += `  Health Score: ${agent.healthScore}\n`;
      message += `  Tasks Completed: ${agent.performance.tasksCompleted}\n`;
      message += `  Last Update: ${agent.lastUpdate.toLocaleTimeString()}\n`;
      if (agent.workProducts.length > 0) {
        message += `  Recent Work: ${agent.workProducts.slice(-3).join(', ')}\n`;
      }
    });
    
    return message;
  }

  public dispose(): void {
    this.stopMonitoring();
    if (this.fileWatcher) {
      this.fileWatcher.dispose();
    }
    this.outputChannel.dispose();
    this.statusBarItem.dispose();
  }
}