import axios, { AxiosResponse } from 'axios';

export interface OllamaMessage {
    role: 'system' | 'user' | 'assistant';
    content: string;
}

export interface OllamaResponse {
    model: string;
    created_at: string;
    message: {
        role: string;
        content: string;
    };
    done: boolean;
}

export interface OllamaModel {
    name: string;
    size: number;
    digest: string;
    modified_at: string;
}

export class OllamaClient {
    private baseUrl: string;
    private timeout: number;

    constructor(baseUrl: string = 'http://localhost:11434', timeout: number = 30000) {
        this.baseUrl = baseUrl;
        this.timeout = timeout;
    }

    /**
     * Check if Ollama server is running and accessible
     */
    async isAvailable(): Promise<boolean> {
        try {
            const response = await axios.get(`${this.baseUrl}/api/tags`, {
                timeout: 5000
            });
            return response.status === 200;
        } catch (error) {
            return false;
        }
    }

    /**
     * Get list of available models
     */
    async getModels(): Promise<OllamaModel[]> {
        try {
            const response: AxiosResponse<{ models: OllamaModel[] }> = await axios.get(
                `${this.baseUrl}/api/tags`,
                { timeout: this.timeout }
            );
            return response.data.models || [];
        } catch (error) {
            console.error('Error fetching models:', error);
            throw new Error('Failed to fetch available models from Ollama');
        }
    }

    /**
     * Send a chat message to Ollama
     */
    async chat(
        model: string,
        messages: OllamaMessage[],
        options?: {
            temperature?: number;
            top_p?: number;
            top_k?: number;
        }
    ): Promise<string> {
        try {
            const response: AxiosResponse<OllamaResponse> = await axios.post(
                `${this.baseUrl}/api/chat`,
                {
                    model,
                    messages,
                    stream: false,
                    options: options || {}
                },
                {
                    timeout: this.timeout,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }
            );

            return response.data.message.content;
        } catch (error) {
            console.error('Error in chat request:', error);
            if (axios.isAxiosError(error)) {
                if (error.response?.status === 404) {
                    throw new Error(`Model '${model}' not found. Please ensure it's installed in Ollama.`);
                }
                if (error.code === 'ECONNREFUSED') {
                    throw new Error('Cannot connect to Ollama. Please ensure Ollama is running on localhost:11434');
                }
            }
            throw new Error('Failed to get response from Ollama');
        }
    }

    /**
     * Stream chat response from Ollama (for future use)
     */
    async streamChat(
        model: string,
        messages: OllamaMessage[],
        onChunk: (chunk: string) => void,
        options?: {
            temperature?: number;
            top_p?: number;
            top_k?: number;
        }
    ): Promise<void> {
        try {
            const response = await axios.post(
                `${this.baseUrl}/api/chat`,
                {
                    model,
                    messages,
                    stream: true,
                    options: options || {}
                },
                {
                    timeout: this.timeout,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    responseType: 'stream'
                }
            );

            response.data.on('data', (chunk: Buffer) => {
                const lines = chunk.toString().split('\n').filter(line => line.trim());
                for (const line of lines) {
                    try {
                        const data = JSON.parse(line);
                        if (data.message?.content) {
                            onChunk(data.message.content);
                        }
                    } catch (e) {
                        // Ignore malformed JSON chunks
                    }
                }
            });

            return new Promise((resolve, reject) => {
                response.data.on('end', resolve);
                response.data.on('error', reject);
            });
        } catch (error) {
            console.error('Error in stream chat request:', error);
            throw new Error('Failed to stream response from Ollama');
        }
    }
}