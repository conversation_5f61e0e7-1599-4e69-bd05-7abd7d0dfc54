import * as vscode from 'vscode';
import { OllamaClient, OllamaMessage } from './ollama-client';
import { CodeAnalysisService, CodeAnalysisResult } from './code-analysis';

export class ChatWebviewProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'agentzero.chatView';
    
    public _view?: vscode.WebviewView;
    private ollamaClient: OllamaClient;
    private codeAnalysisService: CodeAnalysisService;
    private conversationHistory: OllamaMessage[] = [];
    private currentModel: string = '';

    constructor(private readonly _extensionUri: vscode.Uri) {
        this.ollamaClient = new OllamaClient();
        this.codeAnalysisService = new CodeAnalysisService();
    }

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this._extensionUri]
        };

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(async (data) => {
            switch (data.type) {
                case 'sendMessage':
                    await this.handleSendMessage(data.message);
                    break;
                case 'clearChat':
                    this.clearConversation();
                    break;
                case 'getModels':
                    await this.loadAvailableModels();
                    break;
                case 'selectModel':
                    this.currentModel = data.model;
                    this.sendMessage({ type: 'modelSelected', model: data.model });
                    break;
                case 'ready':
                    await this.initializeChat();
                    break;
                case 'analyzeCode':
                    await this.handleCodeAnalysis(data.code, data.language);
                    break;
            }
        });
    }

    private async initializeChat() {
        // Check if Ollama is available
        const isAvailable = await this.ollamaClient.isAvailable();
        if (!isAvailable) {
            this.sendMessage({
                type: 'error',
                message: 'Ollama is not running. Please start Ollama and try again.'
            });
            return;
        }

        // Load available models
        await this.loadAvailableModels();
    }

    private async loadAvailableModels() {
        try {
            const models = await this.ollamaClient.getModels();
            if (models.length === 0) {
                this.sendMessage({
                    type: 'error',
                    message: 'No models found in Ollama. Please install a model first.'
                });
                return;
            }

            this.sendMessage({
                type: 'modelsLoaded',
                models: models.map(m => m.name)
            });

            // Auto-select first model if none selected
            if (!this.currentModel && models.length > 0) {
                this.currentModel = models[0].name;
                this.sendMessage({
                    type: 'modelSelected',
                    model: this.currentModel
                });
            }
        } catch (error) {
            this.sendMessage({
                type: 'error',
                message: `Failed to load models: ${error instanceof Error ? error.message : 'Unknown error'}`
            });
        }
    }

    private async handleSendMessage(message: string) {
        if (!this.currentModel) {
            this.sendMessage({
                type: 'error',
                message: 'Please select a model first.'
            });
            return;
        }

        // Add user message to conversation
        const userMessage: OllamaMessage = { role: 'user', content: message };
        this.conversationHistory.push(userMessage);

        // Show user message in UI
        this.sendMessage({
            type: 'userMessage',
            message: message
        });

        // Show thinking indicator
        this.sendMessage({ type: 'thinking', thinking: true });

        try {
            // Get AI response
            const response = await this.ollamaClient.chat(
                this.currentModel,
                this.conversationHistory
            );

            // Add AI response to conversation
            const assistantMessage: OllamaMessage = { role: 'assistant', content: response };
            this.conversationHistory.push(assistantMessage);

            // Show AI response in UI
            this.sendMessage({
                type: 'aiMessage',
                message: response
            });
        } catch (error) {
            this.sendMessage({
                type: 'error',
                message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
            });
        } finally {
            // Hide thinking indicator
            this.sendMessage({ type: 'thinking', thinking: false });
        }
    }

    private clearConversation() {
        this.conversationHistory = [];
        this.sendMessage({ type: 'chatCleared' });
    }

    private async handleCodeAnalysis(code: string, language: string) {
        if (!this.currentModel) {
            this.sendMessage({
                type: 'error',
                message: 'Please select a model first.'
            });
            return;
        }

        // Show thinking indicator
        this.sendMessage({ type: 'thinking', thinking: true });

        try {
            const result = await this.codeAnalysisService.analyzeCode(
                code,
                language,
                this.currentModel
            );

            // Format the analysis result for display
            const formattedResult = this.formatAnalysisResult(result);
            
            this.sendMessage({
                type: 'analysisResult',
                result: formattedResult
            });
        } catch (error) {
            this.sendMessage({
                type: 'error',
                message: `Code analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`
            });
        } finally {
            // Hide thinking indicator
            this.sendMessage({ type: 'thinking', thinking: false });
        }
    }

    private formatAnalysisResult(result: CodeAnalysisResult): string {
        let formatted = `## Code Analysis Results\n\n`;
        formatted += `**Summary:** ${result.summary}\n`;
        formatted += `**Confidence:** ${(result.confidence * 100).toFixed(0)}%\n\n`;

        if (result.suggestions.length === 0) {
            formatted += `No specific suggestions found. The code appears to be in good shape!`;
            return formatted;
        }

        formatted += `### Suggestions (${result.suggestions.length}):\n\n`;

        result.suggestions.forEach((suggestion, index) => {
            const priority = suggestion.priority.toUpperCase();
            const typeEmoji = this.getTypeEmoji(suggestion.type);
            
            formatted += `**${index + 1}. ${typeEmoji} ${suggestion.type.toUpperCase()} - ${priority} Priority**\n`;
            formatted += `${suggestion.description}\n\n`;
            formatted += `*Reasoning:* ${suggestion.reasoning}\n\n`;
            
            if (suggestion.originalCode && suggestion.suggestedCode) {
                formatted += `*Current code:*\n\`\`\`\n${suggestion.originalCode}\n\`\`\`\n\n`;
                formatted += `*Suggested improvement:*\n\`\`\`\n${suggestion.suggestedCode}\n\`\`\`\n\n`;
            }
            
            formatted += `---\n\n`;
        });

        return formatted;
    }

    private getTypeEmoji(type: string): string {
        switch (type) {
            case 'bug': return '🐛';
            case 'security': return '🔒';
            case 'performance': return '⚡';
            case 'optimization': return '🚀';
            case 'style': return '🎨';
            case 'improvement': return '💡';
            default: return '📝';
        }
    }

    private sendMessage(message: any) {
        if (this._view) {
            this._view.webview.postMessage(message);
        }
    }

    private _getHtmlForWebview(webview: vscode.Webview) {
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>AgentZero Chat</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    font-size: var(--vscode-font-size);
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                    margin: 0;
                    padding: 10px;
                    height: 100vh;
                    display: flex;
                    flex-direction: column;
                }

                .header {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 10px;
                    padding-bottom: 10px;
                    border-bottom: 1px solid var(--vscode-panel-border);
                }

                .model-selector {
                    flex: 1;
                    margin-right: 10px;
                }

                .model-selector select {
                    width: 100%;
                    background-color: var(--vscode-dropdown-background);
                    color: var(--vscode-dropdown-foreground);
                    border: 1px solid var(--vscode-dropdown-border);
                    padding: 4px 8px;
                    border-radius: 2px;
                }

                .clear-btn {
                    background-color: var(--vscode-button-secondaryBackground);
                    color: var(--vscode-button-secondaryForeground);
                    border: none;
                    padding: 6px 12px;
                    border-radius: 2px;
                    cursor: pointer;
                    font-size: 12px;
                }

                .clear-btn:hover {
                    background-color: var(--vscode-button-secondaryHoverBackground);
                }

                .chat-container {
                    flex: 1;
                    overflow-y: auto;
                    margin-bottom: 10px;
                    padding: 10px;
                    border: 1px solid var(--vscode-panel-border);
                    border-radius: 4px;
                    background-color: var(--vscode-editor-background);
                }

                .message {
                    margin-bottom: 15px;
                    padding: 10px;
                    border-radius: 6px;
                    line-height: 1.4;
                }

                .user-message {
                    background-color: var(--vscode-inputValidation-infoBackground);
                    border-left: 3px solid var(--vscode-inputValidation-infoBorder);
                    margin-left: 20px;
                }

                .ai-message {
                    background-color: var(--vscode-textBlockQuote-background);
                    border-left: 3px solid var(--vscode-textBlockQuote-border);
                    margin-right: 20px;
                }

                .error-message {
                    background-color: var(--vscode-inputValidation-errorBackground);
                    border-left: 3px solid var(--vscode-inputValidation-errorBorder);
                    color: var(--vscode-errorForeground);
                }

                .thinking {
                    font-style: italic;
                    color: var(--vscode-descriptionForeground);
                    text-align: center;
                    padding: 10px;
                }

                .input-container {
                    display: flex;
                    gap: 10px;
                }

                .message-input {
                    flex: 1;
                    background-color: var(--vscode-input-background);
                    color: var(--vscode-input-foreground);
                    border: 1px solid var(--vscode-input-border);
                    padding: 8px 12px;
                    border-radius: 4px;
                    font-family: inherit;
                    resize: vertical;
                    min-height: 20px;
                    max-height: 100px;
                }

                .message-input:focus {
                    outline: none;
                    border-color: var(--vscode-focusBorder);
                }

                .send-btn {
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-weight: bold;
                }

                .send-btn:hover {
                    background-color: var(--vscode-button-hoverBackground);
                }

                .send-btn:disabled {
                    background-color: var(--vscode-button-secondaryBackground);
                    color: var(--vscode-button-secondaryForeground);
                    cursor: not-allowed;
                }

                .welcome-message {
                    text-align: center;
                    color: var(--vscode-descriptionForeground);
                    font-style: italic;
                    padding: 20px;
                }

                pre {
                    background-color: var(--vscode-textCodeBlock-background);
                    padding: 10px;
                    border-radius: 4px;
                    overflow-x: auto;
                    white-space: pre-wrap;
                    word-wrap: break-word;
                }

                code {
                    background-color: var(--vscode-textCodeBlock-background);
                    padding: 2px 4px;
                    border-radius: 2px;
                    font-family: var(--vscode-editor-font-family);
                }
            </style>
        </head>
        <body>
            <div class="header">
                <div class="model-selector">
                    <select id="modelSelect" disabled>
                        <option value="">Loading models...</option>
                    </select>
                </div>
                <button class="clear-btn" onclick="clearChat()">Clear</button>
            </div>

            <div class="chat-container" id="chatContainer">
                <div class="welcome-message">
                    Welcome to AgentZero! Select a model and start chatting.
                </div>
            </div>

            <div class="input-container">
                <textarea 
                    id="messageInput" 
                    class="message-input" 
                    placeholder="Type your message here..."
                    rows="1"
                ></textarea>
                <button id="sendBtn" class="send-btn" onclick="sendMessage()" disabled>Send</button>
            </div>

            <script>
                const vscode = acquireVsCodeApi();
                let isThinking = false;

                // Auto-resize textarea
                const messageInput = document.getElementById('messageInput');
                messageInput.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = Math.min(this.scrollHeight, 100) + 'px';
                });

                // Send message on Enter (but allow Shift+Enter for new lines)
                messageInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendMessage();
                    }
                });

                // Handle messages from extension
                window.addEventListener('message', event => {
                    const message = event.data;
                    
                    switch (message.type) {
                        case 'modelsLoaded':
                            populateModels(message.models);
                            break;
                        case 'modelSelected':
                            document.getElementById('modelSelect').value = message.model;
                            updateSendButton();
                            break;
                        case 'userMessage':
                            addMessage(message.message, 'user');
                            break;
                        case 'aiMessage':
                            addMessage(message.message, 'ai');
                            break;
                        case 'error':
                            addMessage(message.message, 'error');
                            break;
                        case 'thinking':
                            toggleThinking(message.thinking);
                            break;
                        case 'chatCleared':
                            clearChatUI();
                            break;
                        case 'analysisResult':
                            addMessage(message.result, 'ai');
                            break;
                    }
                });

                function populateModels(models) {
                    const select = document.getElementById('modelSelect');
                    select.innerHTML = '<option value="">Select a model...</option>';
                    
                    models.forEach(model => {
                        const option = document.createElement('option');
                        option.value = model;
                        option.textContent = model;
                        select.appendChild(option);
                    });
                    
                    select.disabled = false;
                    select.addEventListener('change', function() {
                        if (this.value) {
                            vscode.postMessage({
                                type: 'selectModel',
                                model: this.value
                            });
                        }
                    });
                }

                function sendMessage() {
                    const input = document.getElementById('messageInput');
                    const message = input.value.trim();
                    
                    if (message && !isThinking) {
                        vscode.postMessage({
                            type: 'sendMessage',
                            message: message
                        });
                        input.value = '';
                        input.style.height = 'auto';
                    }
                }

                function clearChat() {
                    vscode.postMessage({ type: 'clearChat' });
                }

                function clearChatUI() {
                    const container = document.getElementById('chatContainer');
                    container.innerHTML = '<div class="welcome-message">Chat cleared. Start a new conversation!</div>';
                }

                function addMessage(content, type) {
                    const container = document.getElementById('chatContainer');
                    const messageDiv = document.createElement('div');
                    messageDiv.className = \`message \${type}-message\`;
                    
                    // Simple markdown-like formatting
                    const formattedContent = content
                        .replace(/\`\`\`([\\s\\S]*?)\`\`\`/g, '<pre><code>$1</code></pre>')
                        .replace(/\`([^\`]+)\`/g, '<code>$1</code>')
                        .replace(/\\n/g, '<br>');
                    
                    messageDiv.innerHTML = formattedContent;
                    container.appendChild(messageDiv);
                    container.scrollTop = container.scrollHeight;
                    
                    // Remove welcome message if it exists
                    const welcome = container.querySelector('.welcome-message');
                    if (welcome) {
                        welcome.remove();
                    }
                }

                function toggleThinking(thinking) {
                    isThinking = thinking;
                    updateSendButton();
                    
                    const container = document.getElementById('chatContainer');
                    let thinkingDiv = container.querySelector('.thinking');
                    
                    if (thinking) {
                        if (!thinkingDiv) {
                            thinkingDiv = document.createElement('div');
                            thinkingDiv.className = 'thinking';
                            thinkingDiv.textContent = 'AI is thinking...';
                            container.appendChild(thinkingDiv);
                            container.scrollTop = container.scrollHeight;
                        }
                    } else {
                        if (thinkingDiv) {
                            thinkingDiv.remove();
                        }
                    }
                }

                function updateSendButton() {
                    const sendBtn = document.getElementById('sendBtn');
                    const modelSelect = document.getElementById('modelSelect');
                    const messageInput = document.getElementById('messageInput');
                    
                    const hasModel = modelSelect.value !== '';
                    const hasMessage = messageInput.value.trim() !== '';
                    
                    sendBtn.disabled = !hasModel || isThinking;
                    messageInput.disabled = isThinking;
                }

                // Enable send button when message is typed
                document.getElementById('messageInput').addEventListener('input', updateSendButton);

                // Initialize
                vscode.postMessage({ type: 'ready' });
            </script>
        </body>
        </html>`;
    }
}