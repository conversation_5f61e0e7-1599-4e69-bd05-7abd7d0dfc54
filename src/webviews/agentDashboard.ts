import * as vscode from 'vscode';
import { <PERSON><PERSON><PERSON>ger, AgentStatus, SystemStatus } from '../services/agentManager';

export class AgentDashboardProvider implements vscode.WebviewViewProvider {
  public static readonly viewType = 'agentzero.agentDashboard';
  private _view?: vscode.WebviewView;

  constructor(
    private readonly _extensionUri: vscode.Uri,
    private readonly agentManager: AgentManager
  ) {
    // Listen to agent manager events
    this.agentManager.on('agentActivity', (data) => {
      this.updateDashboard();
    });
    
    this.agentManager.on('agentCompleted', (data) => {
      this.updateDashboard();
    });
    
    this.agentManager.on('healthCheck', (data) => {
      this.updateDashboard();
    });
  }

  public resolveWebviewView(
    webviewView: vscode.WebviewView,
    context: vscode.WebviewViewResolveContext,
    _token: vscode.CancellationToken
  ) {
    this._view = webviewView;

    webviewView.webview.options = {
      enableScripts: true,
      localResourceRoots: [this._extensionUri]
    };

    webviewView.webview.html = this.getHtmlForWebview(webviewView.webview);

    // Handle messages from the webview
    webviewView.webview.onDidReceiveMessage(
      message => {
        switch (message.command) {
          case 'restartAgent':
            this.agentManager.restartAgent(message.agentId);
            this.updateDashboard();
            break;
          case 'pauseAgent':
            this.agentManager.pauseAgent(message.agentId);
            this.updateDashboard();
            break;
          case 'resumeAgent':
            this.agentManager.resumeAgent(message.agentId);
            this.updateDashboard();
            break;
          case 'refreshDashboard':
            this.updateDashboard();
            break;
          case 'showLogs':
            vscode.commands.executeCommand('workbench.action.output.show.extension-output-agentzero.#1-AgentZero Manager');
            break;
        }
      },
      undefined
    );

    // Initial update
    this.updateDashboard();
    
    // Auto-refresh every 5 seconds
    setInterval(() => {
      this.updateDashboard();
    }, 5000);
  }

  private updateDashboard() {
    if (this._view) {
      const status = this.agentManager.getSystemStatus();
      this._view.webview.postMessage({
        command: 'updateStatus',
        status: status
      });
    }
  }

  private getHtmlForWebview(webview: vscode.Webview): string {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Dashboard</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 16px;
        }
        
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--vscode-panel-border);
        }
        
        .system-status {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .status-healthy { background-color: #4caf50; }
        .status-degraded { background-color: #ff9800; }
        .status-critical { background-color: #f44336; }
        
        .refresh-btn {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .refresh-btn:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        
        .agents-container {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        
        .agent-card {
            border: 1px solid var(--vscode-panel-border);
            border-radius: 8px;
            padding: 16px;
            background-color: var(--vscode-editor-background);
        }
        
        .agent-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .agent-name {
            font-weight: bold;
            font-size: 14px;
        }
        
        .agent-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-idle { background-color: #6c757d; color: white; }
        .status-running { background-color: #007acc; color: white; }
        .status-completed { background-color: #28a745; color: white; }
        .status-failed { background-color: #dc3545; color: white; }
        
        .agent-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 12px;
        }
        
        .metric {
            text-align: center;
        }
        
        .metric-value {
            font-size: 18px;
            font-weight: bold;
            color: var(--vscode-textLink-foreground);
        }
        
        .metric-label {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            text-transform: uppercase;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background-color: var(--vscode-progressBar-background);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 12px;
        }
        
        .progress-fill {
            height: 100%;
            background-color: var(--vscode-progressBar-background);
            transition: width 0.3s ease;
        }
        
        .agent-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
        }
        
        .action-btn:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }
        
        .work-products {
            margin-top: 12px;
            font-size: 12px;
        }
        
        .work-products-title {
            font-weight: bold;
            margin-bottom: 4px;
            color: var(--vscode-descriptionForeground);
        }
        
        .work-item {
            padding: 2px 0;
            color: var(--vscode-textLink-foreground);
        }
        
        .system-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 12px;
            margin-bottom: 20px;
            padding: 12px;
            background-color: var(--vscode-editor-inactiveSelectionBackground);
            border-radius: 6px;
        }
        
        .last-update {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            text-align: center;
            margin-top: 16px;
        }
    </style>
</head>
<body>
    <div class="dashboard-header">
        <h2>Agent Dashboard</h2>
        <button class="refresh-btn" onclick="refreshDashboard()">Refresh</button>
    </div>
    
    <div class="system-status">
        <div class="status-indicator" id="systemStatusIndicator"></div>
        <span id="systemStatusText">Loading...</span>
    </div>
    
    <div class="system-metrics" id="systemMetrics">
        <!-- System metrics will be populated here -->
    </div>
    
    <div class="agents-container" id="agentsContainer">
        <!-- Agent cards will be populated here -->
    </div>
    
    <div class="last-update" id="lastUpdate">
        Last updated: Loading...
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        
        function refreshDashboard() {
            vscode.postMessage({ command: 'refreshDashboard' });
        }
        
        function restartAgent(agentId) {
            vscode.postMessage({ command: 'restartAgent', agentId: agentId });
        }
        
        function pauseAgent(agentId) {
            vscode.postMessage({ command: 'pauseAgent', agentId: agentId });
        }
        
        function resumeAgent(agentId) {
            vscode.postMessage({ command: 'resumeAgent', agentId: agentId });
        }
        
        function showLogs() {
            vscode.postMessage({ command: 'showLogs' });
        }
        
        function updateSystemStatus(status) {
            const indicator = document.getElementById('systemStatusIndicator');
            const text = document.getElementById('systemStatusText');
            
            indicator.className = 'status-indicator status-' + status.systemHealth;
            text.textContent = 'System Health: ' + status.systemHealth.charAt(0).toUpperCase() + status.systemHealth.slice(1);
        }
        
        function updateSystemMetrics(status) {
            const container = document.getElementById('systemMetrics');
            container.innerHTML = \`
                <div class="metric">
                    <div class="metric-value">\${status.activeTasks}</div>
                    <div class="metric-label">Active Tasks</div>
                </div>
                <div class="metric">
                    <div class="metric-value">\${status.totalTasks}</div>
                    <div class="metric-label">Total Completed</div>
                </div>
                <div class="metric">
                    <div class="metric-value">\${status.executionReady ? 'Yes' : 'No'}</div>
                    <div class="metric-label">Execution Ready</div>
                </div>
                <div class="metric">
                    <div class="metric-value">\${status.agents.length}</div>
                    <div class="metric-label">Total Agents</div>
                </div>
            \`;
        }
        
        function updateAgents(agents) {
            const container = document.getElementById('agentsContainer');
            container.innerHTML = agents.map(agent => \`
                <div class="agent-card">
                    <div class="agent-header">
                        <div class="agent-name">\${agent.name}</div>
                        <div class="agent-status status-\${agent.status}">\${agent.status}</div>
                    </div>
                    
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: \${agent.progress}%; background-color: \${getProgressColor(agent.progress)}"></div>
                    </div>
                    
                    <div class="agent-metrics">
                        <div class="metric">
                            <div class="metric-value">\${agent.healthScore}</div>
                            <div class="metric-label">Health Score</div>
                        </div>
                        <div class="metric">
                            <div class="metric-value">\${agent.performance.tasksCompleted}</div>
                            <div class="metric-label">Tasks Done</div>
                        </div>
                    </div>
                    
                    <div class="agent-actions">
                        <button class="action-btn" onclick="restartAgent('\${agent.agentId}')">Restart</button>
                        \${agent.status === 'running' ? 
                            \`<button class="action-btn" onclick="pauseAgent('\${agent.agentId}')">Pause</button>\` :
                            \`<button class="action-btn" onclick="resumeAgent('\${agent.agentId}')">Resume</button>\`
                        }
                    </div>
                    
                    \${agent.workProducts.length > 0 ? \`
                        <div class="work-products">
                            <div class="work-products-title">Recent Work:</div>
                            \${agent.workProducts.slice(-3).map(item => \`<div class="work-item">• \${item}</div>\`).join('')}
                        </div>
                    \` : ''}
                </div>
            \`).join('');
        }
        
        function getProgressColor(progress) {
            if (progress < 30) return '#f44336';
            if (progress < 70) return '#ff9800';
            return '#4caf50';
        }
        
        function updateLastUpdate() {
            document.getElementById('lastUpdate').textContent = 'Last updated: ' + new Date().toLocaleTimeString();
        }
        
        // Listen for messages from the extension
        window.addEventListener('message', event => {
            const message = event.data;
            switch (message.command) {
                case 'updateStatus':
                    updateSystemStatus(message.status);
                    updateSystemMetrics(message.status);
                    updateAgents(message.status.agents);
                    updateLastUpdate();
                    break;
            }
        });
        
        // Initial load
        refreshDashboard();
    </script>
</body>
</html>`;
  }
}