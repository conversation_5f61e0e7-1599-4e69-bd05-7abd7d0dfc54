{"name": "agentzero", "displayName": "AgentZero", "description": "AI Agent for developers", "version": "0.0.1", "engines": {"vscode": "^1.102.0"}, "categories": ["Other"], "activationEvents": [], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "agentzero.helloWorld", "title": "Hello World"}, {"command": "agentzero.reverseText", "title": "Reverse Selected Text"}, {"command": "agentzero.openChat", "title": "Open Agent<PERSON><PERSON>"}, {"command": "agentzero.analyzeCode", "title": "Analyze Selected Code with AI"}, {"command": "agentzero.showAgentStatus", "title": "Show Agent Status"}, {"command": "agentzero.startMonitoring", "title": "Start Agent Monitoring"}, {"command": "agentzero.stopMonitoring", "title": "Stop Agent Monitoring"}, {"command": "agentzero.openAgentDashboard", "title": "Open Agent Dashboard"}], "views": {"agentzero-chat": [{"id": "agentzero.chatView", "name": "Cha<PERSON>", "type": "webview"}], "agentzero-agents": [{"id": "agentzero.agentDashboard", "name": "Agent <PERSON>", "type": "webview"}]}, "viewsContainers": {"activitybar": [{"id": "agentzero-chat", "title": "Agent<PERSON><PERSON>", "icon": "$(comment-discussion)"}, {"id": "agentzero-agents", "title": "AgentZero Agents", "icon": "$(organization)"}]}, "menus": {"editor/context": [{"command": "agentzero.analyzeCode", "when": "editorHasSelection", "group": "agentzero"}]}}, "scripts": {"vscode:prepublish": "pnpm run package", "compile": "pnpm run check-types && pnpm run lint && node esbuild.js", "watch": "npm-run-all -p watch:*", "watch:esbuild": "node esbuild.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "package": "pnpm run check-types && pnpm run lint && node esbuild.js --production", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "pnpm run compile-tests && pnpm run compile && pnpm run lint", "check-types": "tsc --noEmit", "lint": "eslint src", "test": "vscode-test"}, "devDependencies": {"@types/vscode": "^1.102.0", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "eslint": "^9.25.1", "esbuild": "^0.25.3", "npm-run-all": "^4.1.5", "typescript": "^5.8.3", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.5.2"}, "dependencies": {"axios": "^1.6.0"}}