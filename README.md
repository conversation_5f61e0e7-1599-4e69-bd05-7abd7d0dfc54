# AgentZero - AI-Powered Code Assistant

Agent<PERSON><PERSON> is a VSCode extension that provides an intelligent chat interface powered by local Ollama models for code analysis, improvement suggestions, and iterative development assistance.

## Features

### 🤖 AI Chat Interface
- Interactive chat with local Ollama models
- Model selection and management
- Conversation history
- Clear, modern UI integrated into VSCode

### 🔍 Code Analysis
- Analyze selected code for improvements, bugs, and optimizations
- Get detailed suggestions with explanations
- Priority-based recommendations
- Support for multiple programming languages

### 💡 Smart Suggestions
- Performance optimizations
- Code style improvements
- Security vulnerability detection
- Bug identification and fixes
- Best practice recommendations

## Prerequisites

1. **Ollama Installation**: Install Ollama on your system
   ```bash
   # macOS
   brew install ollama
   
   # Linux
   curl -fsSL https://ollama.ai/install.sh | sh
   ```

2. **Download a Model**: Install at least one language model
   ```bash
   # Example: Install CodeLlama for code tasks
   ollama pull codellama
   
   # Or install Llama 2 for general chat
   ollama pull llama2
   ```

3. **Start Ollama**: Ensure Ollama is running
   ```bash
   ollama serve
   ```

## Installation

1. Install the extension from the VSCode marketplace (when published)
2. Or install from VSIX:
   - Download the `.vsix` file
   - Open VSCode
   - Go to Extensions view (`Ctrl+Shift+X`)
   - Click "..." menu and select "Install from VSIX..."

## Usage

### Opening the Chat Interface

1. **Via Activity Bar**: Click the AgentZero icon in the activity bar
2. **Via Command Palette**: 
   - Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on macOS)
   - Type "Open AgentZero Chat"
   - Press Enter

### Analyzing Code

1. **Select code** in any editor
2. **Right-click** and select "Analyze Selected Code with AI"
3. **Or use Command Palette**: "Analyze Selected Code with AI"
4. View results in the chat interface

### Chat Features

- **Model Selection**: Choose from available Ollama models
- **Send Messages**: Type questions or requests about code
- **Clear Chat**: Reset conversation history
- **Code Formatting**: Automatic syntax highlighting for code blocks

## Supported Languages

The extension works with any programming language supported by your chosen Ollama model. Popular models like CodeLlama work well with:

- JavaScript/TypeScript
- Python
- Java
- C/C++
- Go
- Rust
- PHP
- And many more...

## Configuration

The extension automatically connects to Ollama on `localhost:11434`. If you're running Ollama on a different host or port, you may need to modify the connection settings in the source code.

## Development

### Building from Source

1. Clone the repository
2. Install dependencies:
   ```bash
   pnpm install
   ```
3. Compile the extension:
   ```bash
   pnpm run compile
   ```
4. Package the extension:
   ```bash
   pnpm run package
   ```

### Project Structure

```
src/
├── extension.ts          # Main extension entry point
├── chat-webview.ts       # Chat interface implementation
├── ollama-client.ts      # Ollama API client
└── code-analysis.ts      # Code analysis service
```

## Troubleshooting

### Ollama Not Found
- Ensure Ollama is installed and running
- Check that it's accessible on `localhost:11434`
- Verify at least one model is downloaded

### No Models Available
- Download a model: `ollama pull codellama`
- Restart the extension
- Refresh the chat interface

### Analysis Not Working
- Ensure you have selected code before analyzing
- Check that a model is selected in the chat interface
- Verify Ollama is responding correctly

## Privacy & Security

- All processing happens locally with your Ollama installation
- No code is sent to external services
- Conversation history is stored locally in VSCode
- Models run entirely on your machine

## Contributing

Contributions are welcome! Please feel free to submit issues and pull requests.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Roadmap

- [ ] Streaming responses for better UX
- [ ] Code refactoring suggestions with diff view
- [ ] Integration with version control
- [ ] Custom model configurations
- [ ] Batch file analysis
- [ ] Export analysis reports
- [ ] Plugin system for custom analyzers

## Support

If you encounter any issues or have questions:

1. Check the troubleshooting section above
2. Search existing issues on GitHub
3. Create a new issue with detailed information about your problem

---

**Note**: This extension requires a local Ollama installation and is designed for developers who want to keep their code analysis completely private and local.